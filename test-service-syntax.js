#!/usr/bin/env node

/**
 * Test script to check service syntax
 */

console.log("🔍 Testing Service Syntax");
console.log("=".repeat(40));

try {
  // Try to require the service
  const service = require('./services/ari-handler/ariHandler.service.js');
  
  console.log("✅ Service file loaded successfully");
  console.log("Service name:", service.name);
  
  // Check if methods object exists
  if (service.methods) {
    console.log("✅ Methods object exists");
    
    // Check if startExternalMediaStream method exists
    if (typeof service.methods.startExternalMediaStream === 'function') {
      console.log("✅ startExternalMediaStream method exists");
    } else {
      console.log("❌ startExternalMediaStream method NOT found");
      
      // List available methods
      const methods = Object.keys(service.methods).filter(key => 
        typeof service.methods[key] === 'function'
      );
      
      console.log("Available methods:", methods.length);
      methods.forEach(method => {
        console.log(`  - ${method}`);
      });
    }
  } else {
    console.log("❌ Methods object NOT found");
    console.log("Service keys:", Object.keys(service));
  }
  
} catch (error) {
  console.log("❌ Error loading service:", error.message);
  
  if (error.message.includes('SyntaxError')) {
    console.log("🔍 Syntax error detected. Check the service file for:");
    console.log("  - Missing commas");
    console.log("  - Unmatched braces");
    console.log("  - Invalid JavaScript syntax");
  }
}
