#!/usr/bin/env node

/**
 * Test script to directly test service methods
 */

const { ServiceBroker } = require("moleculer");

async function testDirectCall() {
  console.log("🧪 Testing Direct Service Call");
  console.log("=".repeat(40));

  // Create a test broker
  const broker = new ServiceBroker({
    logger: true,
    logLevel: "info"
  });

  try {
    // Load the service
    console.log("📦 Loading ARI Handler service...");
    broker.loadService("./services/ari-handler/ariHandler.service.js");

    // Start the broker
    console.log("🚀 Starting broker...");
    await broker.start();

    // Wait a bit for services to initialize
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Test if service is available
    const services = broker.registry.getServiceList();
    const ariService = services.find(s => s.name === 'ariHandler');
    
    if (ariService) {
      console.log("✅ ARI Handler service found");
      console.log("Available actions:", ariService.actions ? Object.keys(ariService.actions) : 'None');
    } else {
      console.log("❌ ARI Handler service NOT found");
      console.log("Available services:", services.map(s => s.name));
    }

    // Try to call a test method if available
    try {
      console.log("\n🔍 Testing method call...");
      
      // Check if we can access the service instance
      const serviceInstance = broker.getLocalService('ariHandler');
      if (serviceInstance) {
        console.log("✅ Service instance found");
        
        // Check if testMethod exists
        if (typeof serviceInstance.testMethod === 'function') {
          console.log("✅ testMethod exists");
          const result = serviceInstance.testMethod();
          console.log("Test result:", result);
        } else {
          console.log("❌ testMethod NOT found");
        }
        
        // Check if startExternalMediaStream exists
        if (typeof serviceInstance.startExternalMediaStream === 'function') {
          console.log("✅ startExternalMediaStream exists");
        } else {
          console.log("❌ startExternalMediaStream NOT found");
        }
        
        // List all methods
        const methods = Object.getOwnPropertyNames(serviceInstance)
          .filter(prop => typeof serviceInstance[prop] === 'function')
          .filter(prop => !prop.startsWith('_') && prop !== 'constructor');
        
        console.log("Available methods:", methods);
        
      } else {
        console.log("❌ Service instance NOT found");
      }
      
    } catch (callError) {
      console.log("❌ Error calling method:", callError.message);
    }

  } catch (error) {
    console.log("❌ Error:", error.message);
  } finally {
    // Stop the broker
    console.log("\n🛑 Stopping broker...");
    await broker.stop();
  }
}

// Run the test
if (require.main === module) {
  testDirectCall().catch(console.error);
}

module.exports = { testDirectCall };
