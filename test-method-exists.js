#!/usr/bin/env node

/**
 * Test script to check if startExternalMediaStream method exists
 */

const ariHandlerService = require('./services/ari-handler/ariHandler.service.js');

console.log("🔍 Testing ARI Handler Service Method");
console.log("=".repeat(50));

// Check if the service object is valid
console.log("Service object:", typeof ariHandlerService);
console.log("Service name:", ariHandlerService.name);

// Check if startExternalMediaStream method exists
const methodExists = typeof ariHandlerService.startExternalMediaStream === 'function';
console.log("startExternalMediaStream method exists:", methodExists);

if (methodExists) {
  console.log("✅ Method is defined correctly");
} else {
  console.log("❌ Method is NOT defined");
  console.log("Available methods:");
  
  // List all methods in the service
  const methods = Object.getOwnPropertyNames(ariHandlerService)
    .filter(prop => typeof ariHandlerService[prop] === 'function');
  
  methods.forEach(method => {
    console.log(`  - ${method}`);
  });
  
  // Check if it's in the service object structure
  console.log("\nService structure:");
  console.log("Keys:", Object.keys(ariHandlerService));
}

// Check for syntax errors by trying to parse the file
console.log("\n🔍 Checking for syntax errors...");
try {
  const fs = require('fs');
  const serviceCode = fs.readFileSync('./services/ari-handler/ariHandler.service.js', 'utf8');
  
  // Look for the method definition
  const methodRegex = /async\s+startExternalMediaStream\s*\(/;
  const methodFound = methodRegex.test(serviceCode);
  
  console.log("Method found in source code:", methodFound);
  
  if (methodFound) {
    console.log("✅ Method is defined in source code");
  } else {
    console.log("❌ Method NOT found in source code");
  }
  
} catch (error) {
  console.log("❌ Error reading service file:", error.message);
}
